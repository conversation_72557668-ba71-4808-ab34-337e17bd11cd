<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">Search Results</title>
    <meta name="description" content="Search results page">
    
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/results.js" as="script">
    
    <!-- Critical CSS for results page -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: arial, sans-serif;
            background: #fff;
            color: #202124;
            line-height: 1.4;
            font-size: 14px;
            margin: 0;
            padding: 0;
        }
        
        .header {
            padding: 6px 20px 0 20px;
            border-bottom: 1px solid #dadce0;
            background: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: none;
            display: flex;
            align-items: center;
            gap: 30px;
            min-height: 58px;
        }

        .logo {
            text-decoration: none;
            margin-right: 10px;
        }

        .logo svg {
            width: 92px;
            height: 30px;
        }

        .search-container {
            flex: 1;
            max-width: 584px;
            position: relative;
        }

        .search-box {
            width: 100%;
            height: 44px;
            border: 1px solid #dfe1e5;
            border-radius: 24px;
            padding: 0 45px 0 16px;
            font-size: 16px;
            outline: none;
            font-family: arial, sans-serif;
            transition: box-shadow 0.2s ease;
        }

        .search-box:focus {
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            border-color: rgba(223,225,229,0);
        }

        .search-icons {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-icon, .voice-icon, .camera-icon {
            width: 24px;
            height: 24px;
            padding: 8px;
            cursor: pointer;
            border-radius: 50%;
            transition: background-color 0.1s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-icon:hover, .voice-icon:hover, .camera-icon:hover {
            background-color: rgba(60,64,67,.08);
        }

        .search-icon svg, .voice-icon svg, .camera-icon svg {
            width: 16px;
            height: 16px;
            fill: #9aa0a6;
        }

        .voice-icon svg {
            fill: #4285f4;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-left: auto;
        }

        .apps-menu {
            width: 24px;
            height: 24px;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: background-color 0.1s ease;
        }

        .apps-menu:hover {
            background-color: rgba(60,64,67,.08);
        }

        .profile-pic {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(45deg, #4285f4, #34a853, #fbbc05, #ea4335);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }
        
        .nav-tabs {
            background: white;
            padding: 0 20px;
        }

        .nav-content {
            max-width: none;
            display: flex;
            gap: 0;
            align-items: center;
        }

        .nav-tab {
            padding: 12px 16px;
            color: #5f6368;
            text-decoration: none;
            font-size: 13px;
            border-bottom: 3px solid transparent;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .nav-tab.active {
            color: #1a73e8;
            border-bottom-color: #1a73e8;
        }

        .nav-tab:hover {
            color: #1a73e8;
        }

        .nav-tab svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        .main-content {
            max-width: none;
            padding: 20px;
            margin-left: 150px;
        }

        .results-info {
            color: #70757a;
            font-size: 13px;
            margin-bottom: 20px;
            padding-left: 12px;
        }

        .result-item {
            margin-bottom: 28px;
            max-width: 600px;
            padding-left: 12px;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result-url {
            color: #202124;
            font-size: 14px;
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 1.3;
        }

        .result-url .favicon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #f1f3f4;
        }

        .result-url .breadcrumb {
            color: #202124;
            font-size: 14px;
        }

        .result-title {
            color: #1a0dab;
            font-size: 20px;
            text-decoration: none;
            display: block;
            margin-bottom: 3px;
            line-height: 1.3;
            font-weight: 400;
            transition: text-decoration 0.1s ease;
        }

        .result-title:hover {
            text-decoration: underline;
        }

        .result-title:visited {
            color: #609;
        }

        .result-snippet {
            color: #4d5156;
            font-size: 14px;
            line-height: 1.58;
        }

        .result-snippet em {
            font-style: normal;
            font-weight: bold;
        }

        .result-type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
            margin-left: 8px;
            color: white;
        }

        .result-type-web {
            background-color: #4285f4;
        }

        .result-type-images {
            background-color: #ea4335;
        }

        .result-type-videos {
            background-color: #ff6d01;
        }

        .result-type-news {
            background-color: #34a853;
        }

        .result-type-shopping {
            background-color: #fbbc05;
            color: #202124;
        }

        .result-type-books {
            background-color: #9aa0a6;
        }

        /* Help button and modal styles */
        .help-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #4285f4;
            color: white;
            border: none;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .help-button:hover {
            background: #3367d6;
            transform: scale(1.1);
        }

        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1001;
        }

        .help-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-modal h3 {
            margin-top: 0;
            color: #202124;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .help-shortcut {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .help-shortcut:last-child {
            border-bottom: none;
        }

        .help-key {
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            border: 1px solid #dadce0;
        }

        .close-help {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #5f6368;
        }

        /* Search suggestions */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dfe1e5;
            border-top: none;
            border-radius: 0 0 24px 24px;
            box-shadow: 0 2px 5px 1px rgba(64,60,67,.16);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid #f8f9fa;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-icon {
            width: 16px;
            height: 16px;
            opacity: 0.6;
        }

        .suggestion-text {
            flex: 1;
            color: #202124;
            font-size: 14px;
        }

        .suggestion-type {
            color: #5f6368;
            font-size: 12px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #70757a;
        }
        
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4285f4;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .pagination {
            margin-top: 40px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: 0;
            padding-left: 12px;
        }
        
        .page-btn {
            padding: 10px 16px;
            border: none;
            background: none;
            color: #1a73e8;
            cursor: pointer;
            font-size: 14px;
            font-family: arial, sans-serif;
            border-radius: 4px;
            transition: background-color 0.1s ease;
        }

        .page-btn:hover {
            background: #f1f3f4;
        }

        .page-btn.active {
            background: #1a73e8;
            color: white;
        }

        .page-btn:disabled {
            color: #dadce0;
            cursor: not-allowed;
        }

        .pagination-nav {
            display: flex;
            align-items: center;
        }

        .page-number {
            padding: 10px 16px;
            color: #202124;
            font-size: 14px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.1s ease;
        }

        .page-number:hover {
            background: #f1f3f4;
        }

        .page-number.current {
            background: #1a73e8;
            color: white;
        }

        .google-logo-pagination {
            margin: 0 20px;
        }

        .google-logo-pagination svg {
            width: 66px;
            height: 22px;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-wrap: wrap;
                gap: 15px;
            }

            .search-container {
                order: 3;
                width: 100%;
                max-width: none;
            }

            .nav-content {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 0;
            }

            .main-content {
                padding: 15px;
                margin-left: 0;
            }

            .result-item {
                padding-left: 0;
            }

            .results-info {
                padding-left: 0;
            }

            .pagination {
                padding-left: 0;
            }

            .result-title {
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .header {
                padding: 6px 15px 0 15px;
            }

            .nav-content {
                gap: 0;
            }

            .nav-tab {
                padding: 12px 12px;
                font-size: 12px;
            }
        }
    </style>
    
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <header class="header">
        <div class="header-content">
            <a href="index.html" class="logo">
                <svg viewBox="0 0 272 92" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0)">
                        <path d="M115.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18C71.25 34.32 81.24 25 93.5 25s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44S80.99 39.2 80.99 47.18c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#EA4335"/>
                        <path d="M163.75 47.18c0 12.77-9.99 22.18-22.25 22.18s-22.25-9.41-22.25-22.18c0-12.85 9.99-22.18 22.25-22.18s22.25 9.32 22.25 22.18zm-9.74 0c0-7.98-5.79-13.44-12.51-13.44s-12.51 5.46-12.51 13.44c0 7.9 5.79 13.44 12.51 13.44s12.51-5.55 12.51-13.44z" fill="#FBBC05"/>
                        <path d="M209.75 26.34v39.82c0 16.38-9.66 23.07-21.08 23.07-10.75 0-17.22-7.19-19.66-13.07l8.48-3.53c1.51 3.61 5.21 7.87 11.17 7.87 7.31 0 11.84-4.51 11.84-13v-3.19h-.34c-2.18 2.69-6.38 5.04-11.68 5.04-11.09 0-21.25-9.66-21.25-22.09 0-12.52 10.16-22.26 21.25-22.26 5.29 0 9.49 2.35 11.68 4.96h.34v-3.61h9.25zm-8.56 20.92c0-7.81-5.21-13.52-11.84-13.52-6.72 0-12.35 5.71-12.35 13.52 0 7.73 5.63 13.36 12.35 13.36 6.63 0 11.84-5.63 11.84-13.36z" fill="#4285F4"/>
                        <path d="M225 3v65h-9.5V3h9.5z" fill="#34A853"/>
                        <path d="M262.02 54.48l7.56 5.04c-2.44 3.61-8.32 9.83-18.48 9.83-12.6 0-22.01-9.74-22.01-22.18 0-13.19 9.49-22.18 20.92-22.18 11.51 0 17.14 9.16 18.98 14.11l1.01 2.52-29.65 12.28c2.27 4.45 5.8 6.72 10.75 6.72 4.96 0 8.4-2.44 10.92-6.14zm-23.27-7.98l19.82-8.23c-1.09-2.77-4.37-4.7-8.23-4.7-4.95 0-11.84 4.37-11.59 12.93z" fill="#EA4335"/>
                        <path d="M35.29 41.41V32H67c.31 1.64.47 3.58.47 5.68 0 7.06-1.93 15.79-8.15 22.01-6.05 6.3-13.78 9.66-24.02 9.66C16.32 69.35.36 53.89.36 34.91.36 15.93 16.32.47 35.3.47c10.5 0 17.98 4.12 23.6 9.49l-6.64 6.64c-4.03-3.78-9.49-6.72-16.97-6.72-13.86 0-24.7 11.17-24.7 25.03 0 13.86 10.84 25.03 24.7 25.03 8.99 0 14.11-3.61 17.39-6.89 2.66-2.66 4.41-6.46 5.1-11.65l-22.49.01z" fill="#4285F4"/>
                    </g>
                    <defs>
                        <clipPath id="clip0">
                            <rect width="272" height="92" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
            </a>
            <div class="search-container">
                <input
                    type="text"
                    class="search-box"
                    id="search-input"
                    placeholder=""
                    autocomplete="off"
                    spellcheck="false"
                    maxlength="2048"
                    title="Search"
                >
                <div class="search-icons">
                    <div class="voice-icon" id="voice-search-btn" title="Search by voice">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                            <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                        </svg>
                    </div>
                    <div class="camera-icon" title="Search by image">
                        <svg viewBox="0 0 24 24">
                            <path d="M14,6H10L8,4H6A2,2 0 0,0 4,6V18A2,2 0 0,0 6,20H18A2,2 0 0,0 20,18V8A2,2 0 0,0 18,6H16L14,6M12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9Z"/>
                        </svg>
                    </div>
                    <div class="search-icon" id="search-btn" title="Search">
                        <svg viewBox="0 0 24 24">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                    </div>
                </div>
                <div class="search-suggestions" id="search-suggestions"></div>
            </div>
            <div class="header-right">
                <div class="apps-menu" title="Google apps">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M6,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM6,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM12,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM16,6c0,1.1 0.9,2 2,2s2,-0.9 2,-2 -0.9,-2 -2,-2 -2,0.9 -2,2zM12,8c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,14c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2zM18,20c1.1,0 2,-0.9 2,-2s-0.9,-2 -2,-2 -2,0.9 -2,2 0.9,2 2,2z"></path>
                    </svg>
                </div>
                <div class="profile-pic" title="Google Account">G</div>
            </div>
        </div>
    </header>

    <nav class="nav-tabs">
        <div class="nav-content">
            <a href="#" class="nav-tab active" data-type="all">
                <svg viewBox="0 0 24 24">
                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
                </svg>
                All
            </a>
            <a href="#" class="nav-tab" data-type="web">
                <svg viewBox="0 0 24 24">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                Web
            </a>
            <a href="#" class="nav-tab" data-type="images">
                <svg viewBox="0 0 24 24">
                    <path d="M21,19V5c0,-1.1 -0.9,-2 -2,-2H5c-1.1,0 -2,0.9 -2,2v14c0,1.1 0.9,2 2,2h14c1.1,0 2,-0.9 2,-2zM8.5,13.5l2.5,3.01L14.5,12l4.5,6H5l3.5,-4.5z"/>
                </svg>
                Images
            </a>
            <a href="#" class="nav-tab" data-type="videos">
                <svg viewBox="0 0 24 24">
                    <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                </svg>
                Videos
            </a>
            <a href="#" class="nav-tab" data-type="news">
                <svg viewBox="0 0 24 24">
                    <path d="M20,11H4V8H20M20,15H13V13H20M20,19H13V17H20M11,19H4V13H11M20.33,4.67L18.67,3L17,4.67L15.33,3L13.67,4.67L12,3L10.33,4.67L8.67,3L7,4.67L5.33,3L3.67,4.67L2,3V19A2,2 0 0,0 4,21H20A2,2 0 0,0 22,19V3L20.33,4.67Z"/>
                </svg>
                News
            </a>
            <a href="#" class="nav-tab" data-type="shopping">
                <svg viewBox="0 0 24 24">
                    <path d="M7,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2zM1,2v2h2l3.6,7.59 -1.35,2.45c-0.16,0.28 -0.25,0.61 -0.25,0.96 0,1.1 0.9,2 2,2h12v-2L7.42,15c-0.14,0 -0.25,-0.11 -0.25,-0.25l0.03,-0.12L8.1,13h7.45c0.75,0 1.41,-0.41 1.75,-1.03L21.7,4H5.21l-0.94,-2L1,2zM17,18c-1.1,0 -2,0.9 -2,2s0.9,2 2,2 2,-0.9 2,-2 -0.9,-2 -2,-2z"/>
                </svg>
                Shopping
            </a>
            <a href="#" class="nav-tab" data-type="books">
                <svg viewBox="0 0 24 24">
                    <path d="M18,2H6C4.9,2 4,2.9 4,4v16c0,1.1 0.9,2 2,2h12c1.1,0 2,-0.9 2,-2V4C20,2.9 19.1,2 18,2zM18,20H6V4h2v7l2.5,-1.5L13,11V4h5V20z"/>
                </svg>
                Books
            </a>
        </div>
    </nav>
    
    <main class="main-content">
        <div class="results-info" id="results-info"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>Searching...</div>
        </div>
        
        <div class="results-container" id="results-container"></div>
        
        <div class="pagination" id="pagination"></div>
    </main>

    <!-- Help Button -->
    <button class="help-button" id="help-button" title="Keyboard Shortcuts (?)">?</button>

    <!-- Help Modal -->
    <div class="help-modal" id="help-modal">
        <div class="help-modal-content">
            <button class="close-help" id="close-help">&times;</button>
            <h3>Keyboard Shortcuts</h3>
            <div class="help-shortcut">
                <span>Focus search box</span>
                <span class="help-key">Ctrl+K</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to All tab</span>
                <span class="help-key">Alt+1</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to Web tab</span>
                <span class="help-key">Alt+2</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to Images tab</span>
                <span class="help-key">Alt+3</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to Videos tab</span>
                <span class="help-key">Alt+4</span>
            </div>
            <div class="help-shortcut">
                <span>Switch to News tab</span>
                <span class="help-key">Alt+5</span>
            </div>
            <div class="help-shortcut">
                <span>Show this help</span>
                <span class="help-key">?</span>
            </div>
        </div>
    </div>
    
    <!-- Additional CSS for Google enhancements -->
    <link rel="stylesheet" href="styles/google-enhancements.css">

    <script>
        // Inline fallback Utils to avoid dependency issues
        window.Utils = {
            searchCache: {
                get: () => null,
                set: () => {},
            },
            checkApiConfiguration: () => false
        };
        console.log('✅ Fallback Utils created');
    </script>

    <script>
        // Working search functionality
        class SearchApp {
            constructor() {
                this.currentQuery = '';
                this.currentPage = 1;
                this.currentType = 'all';
                this.init();
            }

            init() {
                console.log('🔍 SearchApp initializing...');
                this.setupEventListeners();
                this.loadInitialResults();
            }

            setupEventListeners() {
                // Search input
                const searchInput = document.getElementById('search-input');
                if (searchInput) {
                    searchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.performSearch(e.target.value);
                        }
                    });

                    // Focus and select text when clicked
                    searchInput.addEventListener('focus', () => {
                        searchInput.select();
                    });
                }

                // Search button
                const searchBtn = document.getElementById('search-btn');
                if (searchBtn) {
                    searchBtn.addEventListener('click', () => {
                        this.performSearch(searchInput.value);
                    });
                }

                // Navigation tabs
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchTab(tab.dataset.type);
                    });
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', (e) => {
                    // Focus search input with Ctrl+K or Cmd+K
                    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                        e.preventDefault();
                        if (searchInput) {
                            searchInput.focus();
                            searchInput.select();
                        }
                    }

                    // Quick tab switching with Alt + number keys
                    if (e.altKey && e.key >= '1' && e.key <= '6') {
                        e.preventDefault();
                        const tabs = document.querySelectorAll('.nav-tab');
                        const tabIndex = parseInt(e.key) - 1;
                        if (tabs[tabIndex]) {
                            tabs[tabIndex].click();
                        }
                    }

                    // Navigate results with arrow keys
                    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                        const results = document.querySelectorAll('.result-item');
                        if (results.length > 0) {
                            e.preventDefault();
                            // Simple focus management for accessibility
                            const firstResult = results[0].querySelector('.result-title');
                            if (firstResult && e.key === 'ArrowDown') {
                                firstResult.focus();
                            }
                        }
                    }

                    // Show help modal with '?' key
                    if (e.key === '?' && !e.ctrlKey && !e.metaKey && !e.altKey) {
                        e.preventDefault();
                        this.showHelpModal();
                    }

                    // Close help modal with Escape
                    if (e.key === 'Escape') {
                        this.hideHelpModal();
                    }
                });

                // Help button click
                const helpButton = document.getElementById('help-button');
                if (helpButton) {
                    helpButton.addEventListener('click', () => {
                        this.showHelpModal();
                    });
                }

                // Close help modal
                const closeHelp = document.getElementById('close-help');
                if (closeHelp) {
                    closeHelp.addEventListener('click', () => {
                        this.hideHelpModal();
                    });
                }

                // Close help modal when clicking outside
                const helpModal = document.getElementById('help-modal');
                if (helpModal) {
                    helpModal.addEventListener('click', (e) => {
                        if (e.target === helpModal) {
                            this.hideHelpModal();
                        }
                    });
                }
            }

            loadInitialResults() {
                const urlParams = new URLSearchParams(window.location.search);
                const query = urlParams.get('q');

                if (query) {
                    this.currentQuery = query;
                    this.currentType = urlParams.get('type') || 'all';
                    this.currentPage = parseInt(urlParams.get('start')) || 1;

                    // Update search input
                    const searchInput = document.getElementById('search-input');
                    if (searchInput) {
                        searchInput.value = query;
                    }

                    // Update active tab
                    this.updateActiveTab();

                    // Display results
                    this.displayResults();
                } else {
                    this.hideLoading();
                }
            }

            performSearch(query) {
                if (!query.trim()) return;

                this.currentQuery = query;
                this.currentPage = 1;
                this.updateURL();
                this.displayResults();
            }

            switchTab(type) {
                if (!this.currentQuery) return;

                this.currentType = type;
                this.currentPage = 1;

                // Update active tab
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.type === type);
                });

                this.updateURL();
                this.displayResults();
            }

            updateActiveTab() {
                document.querySelectorAll('.nav-tab').forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.type === this.currentType);
                });
            }

            async displayResults() {
                if (!this.currentQuery) {
                    this.hideLoading();
                    return;
                }

                this.showLoading();

                try {
                    // Try real Google Custom Search API first
                    const results = await this.searchGoogle(this.currentQuery, this.currentPage, this.currentType);
                    this.renderResults(results, true);
                    this.hideLoading();
                    this.showApiStatus('✅ Showing real Google search results', 'success');
                } catch (error) {
                    console.log('API failed, using demo results:', error.message);
                    // Fallback to demo results
                    try {
                        const demoResults = this.getDemoResults(this.currentQuery, this.currentPage, this.currentType);
                        this.renderResults(demoResults, false);
                        this.hideLoading();
                        this.showApiStatus('⚠️ API not available, showing demo results', 'warning');
                    } catch (demoError) {
                        console.error('Demo results failed:', demoError);
                        this.hideLoading();
                        this.showError('Failed to load results');
                    }
                }
            }

            async searchGoogle(query, start = 1, searchType = 'all') {
                // Direct API configuration
                const API_CONFIG = {
                    GOOGLE_API_KEY: 'AIzaSyDo2zq98fZbNEgjkdsYGAZs-CJcfSBz9OQ',
                    SEARCH_ENGINE_ID: '61201925358ea4e83',
                    BASE_URL: 'https://www.googleapis.com/customsearch/v1',
                    RESULTS_PER_PAGE: 10
                };

                // Handle "all" search type with comprehensive results
                if (searchType === 'all') {
                    return await this.searchAllTypes(query, start, API_CONFIG);
                }

                const actualSearchType = searchType;

                try {
                    // Build API request
                    const params = new URLSearchParams({
                        key: API_CONFIG.GOOGLE_API_KEY,
                        cx: API_CONFIG.SEARCH_ENGINE_ID,
                        q: query,
                        start: start,
                        num: API_CONFIG.RESULTS_PER_PAGE
                    });

                    // Add search type specific parameters
                    if (searchType === 'images') {
                        params.append('searchType', 'image');
                    } else if (searchType === 'videos') {
                        params.set('q', `${query} site:youtube.com OR site:vimeo.com`);
                    } else if (searchType === 'news') {
                        params.append('tbm', 'nws');
                    }

                    const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;
                    console.log('🌐 Making API request to:', url.substring(0, 100) + '...');

                    // Add timeout to prevent hanging
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => {
                        console.log('⏰ API request timeout, aborting...');
                        controller.abort();
                    }, 8000); // 8 second timeout

                    const response = await fetch(url, {
                        signal: controller.signal,
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                        }
                    });

                    clearTimeout(timeoutId);

                    console.log('📥 API Response status:', response.status);

                    if (!response.ok) {
                        const errorData = await response.json();
                        console.error('❌ API Error:', errorData);
                        throw new Error(`API Error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
                    }

                    const data = await response.json();
                    console.log('✅ API response received:', data);

                    // Cache the results
                    const cacheKey = `${query}-${start}-${searchType}`;
                    if (window.Utils && window.Utils.searchCache) {
                        window.Utils.searchCache.set(cacheKey, data);
                    }

                    return data;

                } catch (error) {
                    console.error('❌ Search API error:', error);

                    if (error.name === 'AbortError') {
                        console.log('⏰ Request was aborted due to timeout');
                        throw new Error('Search request timed out. Please try again.');
                    }

                    // For any API error, throw it so the caller can handle it
                    throw error;
                }
            }

            async searchAllTypes(query, start = 1, API_CONFIG) {
                console.log('🔍 Performing comprehensive search across all types');

                try {
                    // Perform searches for different content types
                    const searchPromises = [
                        this.performSingleSearch(query, start, 'web', API_CONFIG),
                        this.performSingleSearch(query, 1, 'images', API_CONFIG),
                        this.performSingleSearch(query, 1, 'news', API_CONFIG)
                    ];

                    const results = await Promise.allSettled(searchPromises);
                    console.log('🔍 All search results:', results);

                    // Combine results from all search types
                    const combinedResults = this.combineSearchResults(results, query);

                    return combinedResults;

                } catch (error) {
                    console.error('❌ Comprehensive search failed:', error);
                    throw error;
                }
            }

            async performSingleSearch(query, start, searchType, API_CONFIG) {
                const params = new URLSearchParams({
                    key: API_CONFIG.GOOGLE_API_KEY,
                    cx: API_CONFIG.SEARCH_ENGINE_ID,
                    q: query,
                    start: start,
                    num: searchType === 'web' ? 8 : 3 // Fewer results for non-web types
                });

                // Add search type specific parameters
                if (searchType === 'images') {
                    params.append('searchType', 'image');
                } else if (searchType === 'news') {
                    params.append('tbm', 'nws');
                }

                const url = `${API_CONFIG.BASE_URL}?${params.toString()}`;

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 5000);

                const response = await fetch(url, {
                    signal: controller.signal,
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`${searchType} search failed: ${response.status}`);
                }

                const data = await response.json();
                return { type: searchType, data: data };
            }

            combineSearchResults(results, query) {
                const combinedItems = [];
                let totalResults = 0;
                let searchTime = 0;

                console.log('🔍 Combining search results:', results);

                // Process each search result
                results.forEach((result, index) => {
                    if (result.status === 'fulfilled' && result.value && result.value.data && result.value.data.items) {
                        const resultType = result.value.type;
                        const items = result.value.data.items;

                        // Add items with result type information
                        items.forEach(item => {
                            combinedItems.push({
                                ...item,
                                resultType: resultType,
                                title: resultType !== 'web' ? `[${resultType.toUpperCase()}] ${item.title}` : item.title
                            });
                        });

                        totalResults += parseInt(result.value.data.searchInformation?.totalResults || 0);
                        searchTime = Math.max(searchTime, result.value.data.searchInformation?.searchTime || 0);

                        console.log(`✅ ${resultType} results added:`, items.length);
                    } else {
                        console.log(`❌ ${result.value?.type || 'Unknown'} search failed:`, result);
                    }
                });

                return {
                    searchInformation: {
                        totalResults: totalResults.toString() || "0",
                        searchTime: searchTime || 0.1
                    },
                    items: combinedItems.slice(0, 15) // Limit total results
                };
            }

            getDemoResults(query, start = 1, searchType = 'all') {
                const results = [];
                const startNum = (start - 1) * 10 + 1;

                if (searchType === 'all') {
                    // Create mixed demo results for "all" tab
                    const resultTypes = ['web', 'images', 'news', 'videos'];

                    for (let i = 0; i < 12; i++) {
                        const type = resultTypes[i % resultTypes.length];
                        results.push({
                            title: `[${type.toUpperCase()}] ${query} - Working Demo Result ${startNum + i}`,
                            link: `https://example.com/demo-${type}-result-${startNum + i}`,
                            snippet: `This is a working demo ${type} search result for "${query}". The search functionality is now working correctly! Configure your Google Custom Search API to see real results.`,
                            displayLink: 'example.com',
                            resultType: type
                        });
                    }
                } else {
                    // Single search type demo results
                    for (let i = 0; i < 10; i++) {
                        results.push({
                            title: `${query} - Working Demo ${searchType} Result ${startNum + i}`,
                            link: `https://example.com/demo-${searchType}-result-${startNum + i}`,
                            snippet: `This is a working demo ${searchType} search result for "${query}". The search functionality is now working correctly! Configure your Google Custom Search API to see real results.`,
                            displayLink: 'example.com',
                            resultType: searchType
                        });
                    }
                }

                return {
                    searchInformation: {
                        totalResults: "1234567",
                        searchTime: 0.45
                    },
                    items: results
                };
            }

            renderResults(data, isRealAPI = false) {
                const resultsInfo = document.getElementById('results-info');
                const resultsContainer = document.getElementById('results-container');

                if (!data || !data.items || data.items.length === 0) {
                    this.showNoResults();
                    return;
                }

                // Update results info
                const totalResults = data.searchInformation?.totalResults || data.totalResults || '0';
                const searchTime = data.searchInformation?.searchTime || data.searchTime || 0;
                const sourceText = isRealAPI ? '(Real Google API)' : '(Demo Data)';

                resultsInfo.innerHTML = `
                    About ${parseInt(totalResults).toLocaleString()} results (${searchTime} seconds)
                    <span style="color: ${isRealAPI ? '#34a853' : '#ea4335'}; font-weight: bold;">${sourceText}</span>
                `;

                // Clear previous results
                resultsContainer.innerHTML = '';

                // Render results
                data.items.forEach((item, index) => {
                    const resultDiv = document.createElement('div');
                    resultDiv.className = 'result-item';
                    resultDiv.style.animationDelay = `${index * 0.1}s`;

                    const domain = this.extractDomain(item.link);
                    const highlightedSnippet = this.highlightSearchTerms(item.snippet || '', this.currentQuery);
                    const favicon = this.getFaviconUrl(domain);

                    // Add result type badge for "all" search
                    const resultTypeBadge = this.currentType === 'all' && item.resultType ?
                        `<span class="result-type-badge result-type-${item.resultType}">${item.resultType.toUpperCase()}</span>` : '';

                    resultDiv.innerHTML = `
                        <div class="result-url">
                            <img class="favicon" src="${favicon}" alt="" onerror="this.style.display='none'">
                            <span class="breadcrumb">${this.escapeHtml(item.displayLink || domain)}</span>
                            ${resultTypeBadge}
                        </div>
                        <a href="${this.escapeHtml(item.link)}" class="result-title" target="_blank" rel="noopener">
                            ${this.escapeHtml(item.title)}
                        </a>
                        <div class="result-snippet">${highlightedSnippet}</div>
                    `;

                    resultsContainer.appendChild(resultDiv);
                });

                this.renderPagination();
            }

            showNoResults() {
                const resultsContainer = document.getElementById('results-container');
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #5f6368;">
                        <h3>No results found</h3>
                        <p>Try different keywords or check your spelling</p>
                    </div>
                `;
            }

            showError(message) {
                const resultsContainer = document.getElementById('results-container');
                resultsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #ea4335;">
                        <h3>Something went wrong</h3>
                        <p>${this.escapeHtml(message)}</p>
                        <button onclick="searchApp.displayResults()" style="background: #4285f4; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                            Try again
                        </button>
                    </div>
                `;
            }

            getFaviconUrl(domain) {
                return `https://www.google.com/s2/favicons?domain=${domain}&sz=16`;
            }

            showApiStatus(message, type = 'info') {
                // Remove any existing status message
                const existingStatus = document.getElementById('api-status-message');
                if (existingStatus) {
                    existingStatus.remove();
                }

                const statusDiv = document.createElement('div');
                statusDiv.id = 'api-status-message';

                let backgroundColor, borderColor, textColor;
                switch (type) {
                    case 'success':
                        backgroundColor = '#d4edda';
                        borderColor = '#c3e6cb';
                        textColor = '#155724';
                        break;
                    case 'warning':
                        backgroundColor = '#fff3cd';
                        borderColor = '#ffeaa7';
                        textColor = '#856404';
                        break;
                    case 'error':
                        backgroundColor = '#f8d7da';
                        borderColor = '#f5c6cb';
                        textColor = '#721c24';
                        break;
                    default:
                        backgroundColor = '#d1ecf1';
                        borderColor = '#bee5eb';
                        textColor = '#0c5460';
                }

                statusDiv.innerHTML = `
                    <div style="background: ${backgroundColor}; border: 1px solid ${borderColor}; padding: 15px; margin: 20px; border-radius: 8px; color: ${textColor}; text-align: center;">
                        ${message}
                    </div>
                `;

                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.insertBefore(statusDiv, mainContent.firstChild);
                }

                // Auto-hide after 10 seconds for non-error messages
                if (type !== 'error') {
                    setTimeout(() => {
                        if (statusDiv && statusDiv.parentNode) {
                            statusDiv.remove();
                        }
                    }, 10000);
                }
            }

            renderPagination() {
                const pagination = document.getElementById('pagination');
                const totalPages = 10; // Simplified

                if (totalPages <= 1) {
                    pagination.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                // Previous button
                if (this.currentPage > 1) {
                    paginationHTML += `<button class="page-btn" onclick="searchApp.goToPage(${this.currentPage - 1})">< Previous</button>`;
                }

                // Page numbers
                const startPage = Math.max(1, this.currentPage - 2);
                const endPage = Math.min(totalPages, startPage + 4);

                for (let i = startPage; i <= endPage; i++) {
                    const isActive = i === this.currentPage;
                    paginationHTML += `
                        <button class="page-btn ${isActive ? 'active' : ''}"
                                onclick="searchApp.goToPage(${i})"
                                ${isActive ? 'disabled' : ''}>
                            ${i}
                        </button>
                    `;
                }

                // Next button
                if (this.currentPage < totalPages) {
                    paginationHTML += `<button class="page-btn" onclick="searchApp.goToPage(${this.currentPage + 1})">Next ></button>`;
                }

                pagination.innerHTML = paginationHTML;
            }

            goToPage(page) {
                this.currentPage = page;
                this.updateURL();
                this.displayResults();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }

            showLoading() {
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results-container').style.display = 'none';
            }

            hideLoading() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('results-container').style.display = 'block';
            }

            updateURL() {
                const url = new URL(window.location);
                url.searchParams.set('q', this.currentQuery);
                url.searchParams.set('start', this.currentPage.toString());
                url.searchParams.set('type', this.currentType);

                window.history.pushState({}, '', url.toString());
                document.title = `${this.currentQuery} - Google Clone`;
            }

            extractDomain(url) {
                try {
                    return new URL(url).hostname.replace('www.', '');
                } catch {
                    return url;
                }
            }

            highlightSearchTerms(text, query) {
                if (!query || query.length < 2) return this.escapeHtml(text);

                const terms = query.split(' ').filter(term => term.length > 1);
                let highlightedText = this.escapeHtml(text);

                terms.forEach(term => {
                    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(`(${escapedTerm})`, 'gi');
                    highlightedText = highlightedText.replace(regex, '<em>$1</em>');
                });

                return highlightedText;
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            showApiWarning() {
                console.log('⚠️ Showing API warning');
                // This method is called but we don't need to show a warning since we have fallback demo results
            }

            showHelpModal() {
                const helpModal = document.getElementById('help-modal');
                if (helpModal) {
                    helpModal.style.display = 'block';
                    document.body.style.overflow = 'hidden'; // Prevent background scrolling
                }
            }

            hideHelpModal() {
                const helpModal = document.getElementById('help-modal');
                if (helpModal) {
                    helpModal.style.display = 'none';
                    document.body.style.overflow = 'auto'; // Restore scrolling
                }
            }
        }

        // Initialize the app
        const searchApp = new SearchApp();

        // Make app globally available
        window.searchApp = searchApp;
    </script>
</body>
</html>
